#!/usr/bin/env python3
"""
ATS CV Generator - Command Line Interface

Generate comprehensive CV/resume datasets for testing ATS robustness and parsing capabilities.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path
from typing import List, Dict, Any
import click
from tqdm import tqdm
import colorama
from colorama import Fore, Style

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import config, load_config
from src.models import GenerationRequest, ResumeType, ResumeLayout
from src.batch_processor import BatchProcessor
from src.layout_templates import LayoutTemplateGenerator
from src.utils import (
    setup_logging, validate_distribution_percentages,
    format_duration, format_file_size
)


# Initialize colorama for cross-platform colored output
colorama.init()


class ProgressTracker:
    """Progress tracking for CLI."""
    
    def __init__(self):
        self.pbar = None
        self.current_phase = ""
    
    def __call__(self, job_id: str, progress: float, details: Dict[str, Any]):
        """Progress callback function."""
        status = details.get("status", "unknown")
        phase = details.get("phase", "")
        
        if self.pbar is None and status == "running":
            self.pbar = tqdm(total=100, desc="Generating resumes", unit="%")
        
        if self.pbar:
            if phase != self.current_phase:
                self.current_phase = phase
                phase_descriptions = {
                    "initialization": "Initializing...",
                    "generating_resumes": "Generating resumes...",
                    "resumes_generated": "Resumes generated",
                    "saving_html": "Saving HTML files...",
                    "html_saved": "HTML files saved",
                    "converting_pdf": "Converting to PDF...",
                    "pdf_converted": "PDF conversion complete",
                    "finalizing": "Finalizing..."
                }
                desc = phase_descriptions.get(phase, f"Processing ({phase})")
                self.pbar.set_description(desc)
            
            # Update progress
            current_progress = int(progress)
            if current_progress > self.pbar.n:
                self.pbar.update(current_progress - self.pbar.n)
            
            # Add details to postfix
            postfix = {}
            if "current" in details and "total" in details:
                postfix["resumes"] = f"{details['current']}/{details['total']}"
            if "completed" in details:
                postfix["completed"] = details["completed"]
            if "failed" in details:
                postfix["failed"] = details["failed"]
            
            if postfix:
                self.pbar.set_postfix(postfix)
        
        if status == "completed":
            if self.pbar:
                self.pbar.update(100 - self.pbar.n)
                self.pbar.close()
            
            click.echo(f"\n{Fore.GREEN}✓ Generation completed successfully!{Style.RESET_ALL}")
            click.echo(f"Output directory: {details.get('output_directory', 'N/A')}")
            click.echo(f"Completed resumes: {details.get('completed_resumes', 0)}")
            if details.get('failed_resumes', 0) > 0:
                click.echo(f"{Fore.YELLOW}Failed resumes: {details.get('failed_resumes', 0)}{Style.RESET_ALL}")
        
        elif status == "failed":
            if self.pbar:
                self.pbar.close()
            click.echo(f"\n{Fore.RED}✗ Generation failed: {details.get('error', 'Unknown error')}{Style.RESET_ALL}")


def parse_distribution(distribution_str: str) -> Dict[str, int]:
    """Parse distribution string into dictionary."""
    try:
        parts = distribution_str.split(',')
        if len(parts) != 3:
            raise ValueError("Distribution must have exactly 3 values")
        
        values = [int(part.strip()) for part in parts]
        
        return {
            "high_quality": values[0],
            "edge_cases": values[1],
            "malicious": values[2]
        }
    except ValueError as e:
        raise click.BadParameter(f"Invalid distribution format: {str(e)}")


def validate_templates_directory(templates_path: str) -> List[str]:
    """Validate and collect template files from directory."""
    templates_dir = Path(templates_path)
    
    if not templates_dir.exists():
        raise click.BadParameter(f"Templates directory does not exist: {templates_path}")
    
    if not templates_dir.is_dir():
        raise click.BadParameter(f"Templates path is not a directory: {templates_path}")
    
    # Find JPEG files
    template_files = []
    for ext in ['*.jpg', '*.jpeg', '*.JPG', '*.JPEG']:
        template_files.extend(templates_dir.glob(ext))
    
    if not template_files:
        raise click.BadParameter(f"No JPEG template files found in: {templates_path}")
    
    if len(template_files) > config.templates.max_count:
        raise click.BadParameter(f"Too many templates: {len(template_files)} > {config.templates.max_count}")
    
    return [str(f) for f in template_files]


def validate_layouts(layouts_str: str) -> List[ResumeLayout]:
    """Validate and parse layout descriptions."""
    if not layouts_str:
        raise click.BadParameter("Layouts string cannot be empty")

    layout_names = [name.strip() for name in layouts_str.split(',')]
    layout_descriptions = []

    # Get available layouts
    layout_generator = LayoutTemplateGenerator()
    available_layouts = layout_generator.get_all_layouts()

    for layout_name in layout_names:
        try:
            layout = ResumeLayout(layout_name)
            if layout not in available_layouts:
                raise ValueError(f"Unknown layout: {layout_name}")
            layout_descriptions.append(layout)
        except ValueError:
            # Show available options
            available_names = [layout.value for layout in available_layouts.keys()]
            raise click.BadParameter(
                f"Invalid layout '{layout_name}'. Available layouts: {', '.join(available_names)}"
            )

    if len(layout_descriptions) > 5:
        raise click.BadParameter(f"Too many layouts: {len(layout_descriptions)} > 5")

    return layout_descriptions


def show_available_layouts():
    """Display available layout options."""
    layout_generator = LayoutTemplateGenerator()
    layouts = layout_generator.get_all_layouts()

    click.echo(f"\n{Fore.CYAN}Available Layout Types:{Style.RESET_ALL}")
    for layout, info in layouts.items():
        click.echo(f"\n{Fore.YELLOW}{layout.value}{Style.RESET_ALL}")
        click.echo(f"  Name: {info['name']}")
        click.echo(f"  Description: {info['description']}")
        click.echo(f"  Characteristics:")
        for char in info['characteristics'][:3]:  # Show first 3 characteristics
            click.echo(f"    - {char}")


@click.command()
@click.option(
    '--templates', '-t',
    help='Directory containing JPEG template files (1-50 files)'
)
@click.option(
    '--count', '-c',
    type=int,
    help='Total number of resumes to generate'
)
@click.option(
    '--distribution', '-d',
    help='Distribution ratios as "high_quality,edge_cases,malicious" (must sum to 100)'
)
@click.option(
    '--layouts', '-l',
    help='Comma-separated layout types: classic_chronological, modern_sidebar, minimalist, functional_skills, hybrid_combination'
)
@click.option(
    '--job-desc', '-j',
    help='Job description for content tailoring (100-3000 characters)'
)
@click.option(
    '--output', '-o',
    help='Output directory (default: auto-generated)'
)
@click.option(
    '--config-file',
    default='config.yaml',
    help='Configuration file path'
)
@click.option(
    '--no-pdf',
    is_flag=True,
    help='Skip PDF conversion (HTML only)'
)
@click.option(
    '--verbose', '-v',
    is_flag=True,
    help='Enable verbose logging'
)
@click.option(
    '--dry-run',
    is_flag=True,
    help='Validate parameters without generating resumes'
)
@click.option(
    '--show-layouts',
    is_flag=True,
    help='Show available layout types and exit'
)
def main(templates, layouts, count, distribution, job_desc, output, config_file, no_pdf, verbose, dry_run, show_layouts):
    """
    Generate comprehensive CV/resume datasets for ATS testing.
    
    This tool generates resumes with configurable distribution ratios:
    - High-quality: ATS-friendly, professional formatting
    - Edge cases: Non-standard layouts, multilingual content
    - Malicious: Adversarial content for security testing
    
    Examples:
    
    \b
    # Generate 100 resumes with 70% high-quality, 20% edge cases, 10% malicious
    python generate_resumes.py -t ./templates/ -c 100 -d "70,20,10" -j "Software Engineer position..."
    
    \b
    # Generate 500 resumes with custom output directory
    python generate_resumes.py -t ./templates/ -c 500 -d "80,15,5" -j "Data Scientist role..." -o ./custom_output/
    
    \b
    # Dry run to validate parameters
    python generate_resumes.py -t ./templates/ -c 50 -d "60,25,15" -j "Product Manager..." --dry-run

    \b
    # Generate using layout descriptions instead of template files
    python generate_resumes.py -l "classic_chronological,modern_sidebar,minimalist" -c 100 -d "70,20,10" -j "Software Engineer..."

    \b
    # Show available layout types
    python generate_resumes.py --show-layouts
    """
    
    try:
        # Show layouts if requested
        if show_layouts:
            show_available_layouts()
            return

        # Validate required parameters when not showing layouts
        if not count:
            raise click.BadParameter("--count is required when not using --show-layouts")

        if not distribution:
            raise click.BadParameter("--distribution is required when not using --show-layouts")

        if not job_desc:
            raise click.BadParameter("--job-desc is required when not using --show-layouts")

        # Load configuration
        global config
        if config_file != 'config.yaml':
            config = load_config(config_file)
        
        # Setup logging
        log_level = "DEBUG" if verbose else config.logging.level
        logger = setup_logging(config)
        if verbose:
            logger.setLevel(logging.DEBUG)
        
        click.echo(f"{Fore.CYAN}ATS CV Generator{Style.RESET_ALL}")
        click.echo("=" * 50)
        
        # Validate parameters
        click.echo("Validating parameters...")
        
        # Parse and validate distribution
        distribution_dict = parse_distribution(distribution)
        is_valid, error_msg = validate_distribution_percentages(distribution_dict)
        if not is_valid:
            raise click.BadParameter(error_msg)
        
        # Validate input - either templates or layouts must be provided
        if not templates and not layouts:
            raise click.BadParameter("Either --templates or --layouts must be provided")

        if templates and layouts:
            raise click.BadParameter("Cannot use both --templates and --layouts. Choose one approach.")

        # Process templates or layouts
        template_files = None
        layout_descriptions = None

        if templates:
            template_files = validate_templates_directory(templates)
            click.echo(f"Found {len(template_files)} template files")

        if layouts:
            layout_descriptions = validate_layouts(layouts)
            click.echo(f"Using {len(layout_descriptions)} layout descriptions")
        
        # Validate job description
        if len(job_desc) < config.generation.min_job_description_length:
            raise click.BadParameter(f"Job description too short: {len(job_desc)} < {config.generation.min_job_description_length}")
        
        if len(job_desc) > config.generation.max_job_description_length:
            raise click.BadParameter(f"Job description too long: {len(job_desc)} > {config.generation.max_job_description_length}")
        
        # Validate count
        if count > config.generation.max_total_count:
            raise click.BadParameter(f"Count too high: {count} > {config.generation.max_total_count}")
        
        # Create generation request
        request = GenerationRequest(
            job_description=job_desc,
            total_count=count,
            distribution=distribution_dict,
            template_paths=template_files,
            layout_descriptions=layout_descriptions,
            output_directory=output
        )
        
        # Display summary
        click.echo(f"\n{Fore.YELLOW}Generation Summary:{Style.RESET_ALL}")
        click.echo(f"Total resumes: {count}")
        click.echo(f"Distribution: {distribution_dict}")

        if template_files:
            click.echo(f"Templates: {len(template_files)} files")
        elif layout_descriptions:
            layout_names = [layout.value for layout in layout_descriptions]
            click.echo(f"Layouts: {', '.join(layout_names)}")

        click.echo(f"Job description: {len(job_desc)} characters")
        click.echo(f"PDF conversion: {'Disabled' if no_pdf else 'Enabled'}")
        
        if dry_run:
            click.echo(f"\n{Fore.GREEN}✓ Dry run completed - all parameters are valid{Style.RESET_ALL}")
            return
        
        # Confirm before proceeding
        if not click.confirm(f"\nProceed with generation?"):
            click.echo("Generation cancelled.")
            return
        
        # Run generation
        click.echo(f"\n{Fore.CYAN}Starting generation...{Style.RESET_ALL}")
        
        # Setup progress tracking
        progress_tracker = ProgressTracker()
        
        # Run async generation
        asyncio.run(run_generation(request, not no_pdf, progress_tracker))
        
    except click.BadParameter as e:
        click.echo(f"{Fore.RED}Error: {e}{Style.RESET_ALL}", err=True)
        sys.exit(1)
    except KeyboardInterrupt:
        click.echo(f"\n{Fore.YELLOW}Generation cancelled by user{Style.RESET_ALL}")
        sys.exit(1)
    except Exception as e:
        click.echo(f"{Fore.RED}Unexpected error: {e}{Style.RESET_ALL}", err=True)
        if verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


async def run_generation(request: GenerationRequest, enable_pdf: bool, progress_tracker):
    """Run the generation process."""
    processor = BatchProcessor()
    processor.add_progress_callback(progress_tracker)
    
    try:
        job = await processor.process_generation_request(request, enable_pdf)
        
        # Display final statistics
        if job.status == "completed":
            click.echo(f"\n{Fore.GREEN}Generation Statistics:{Style.RESET_ALL}")
            click.echo(f"Successful resumes: {job.completed_resumes}")
            click.echo(f"Failed resumes: {job.failed_resumes}")
            
            if job.started_at and job.completed_at:
                duration = (job.completed_at - job.started_at).total_seconds()
                click.echo(f"Processing time: {format_duration(duration)}")
                
                if duration > 0:
                    rate = job.completed_resumes / (duration / 60)
                    click.echo(f"Generation rate: {rate:.1f} resumes/minute")
        
    except Exception as e:
        click.echo(f"{Fore.RED}Generation failed: {e}{Style.RESET_ALL}", err=True)
        raise


if __name__ == '__main__':
    main()
