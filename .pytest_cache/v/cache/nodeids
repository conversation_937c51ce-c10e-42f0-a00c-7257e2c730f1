["tests/test_config.py::TestConfig::test_default_config", "tests/test_config.py::TestConfig::test_gemini_config_validation", "tests/test_config.py::TestConfig::test_generation_config_validation", "tests/test_config.py::TestConfig::test_load_config_from_file", "tests/test_config.py::TestConfig::test_load_nonexistent_config", "tests/test_config.py::TestConfigValidation::test_validate_output_config", "tests/test_config.py::TestConfigValidation::test_validate_pdf_config", "tests/test_config.py::TestConfigValidation::test_validate_processing_config"]