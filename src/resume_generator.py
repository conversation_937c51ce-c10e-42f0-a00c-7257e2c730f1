"""Core resume generation engine with configurable distribution ratios."""

import asyncio
import random
import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from pathlib import Path
import json

from .config import config
from .models import (
    ResumeType, GeneratedResume, GenerationRequest, 
    ResumeContent, ValidationResult
)
from .gemini_client import Gemini<PERSON><PERSON>
from .template_processor import TemplateProcessor
from .utils import (
    generate_resume_id, calculate_distribution_counts,
    sanitize_filename, format_duration
)


class ResumeGenerator:
    """Core resume generation engine."""
    
    def __init__(self, gemini_client: Optional[GeminiClient] = None):
        """Initialize resume generator."""
        self.gemini_client = gemini_client or GeminiClient()
        self.template_processor = TemplateProcessor()
        self.logger = logging.getLogger(__name__)
        
        # Generation statistics
        self.generation_stats = {
            "total_generated": 0,
            "successful": 0,
            "failed": 0,
            "by_type": {
                ResumeType.HIGH_QUALITY: {"generated": 0, "successful": 0},
                ResumeType.EDGE_CASES: {"generated": 0, "successful": 0},
                ResumeType.MALICIOUS: {"generated": 0, "successful": 0}
            }
        }
    
    def validate_generation_request(self, request: GenerationRequest) -> ValidationResult:
        """Validate generation request parameters."""
        errors = []
        warnings = []
        
        # Validate job description
        if len(request.job_description) < config.generation.min_job_description_length:
            errors.append(f"Job description too short: {len(request.job_description)} < {config.generation.min_job_description_length}")
        
        if len(request.job_description) > config.generation.max_job_description_length:
            errors.append(f"Job description too long: {len(request.job_description)} > {config.generation.max_job_description_length}")
        
        # Validate total count
        if request.total_count > config.generation.max_total_count:
            errors.append(f"Total count too high: {request.total_count} > {config.generation.max_total_count}")
        
        # Validate distribution
        if sum(request.distribution.values()) != 100:
            errors.append(f"Distribution percentages must sum to 100, got {sum(request.distribution.values())}")
        
        # Validate templates or layouts
        if request.template_paths:
            template_validation = self.template_processor.validate_templates(request.template_paths)
            errors.extend(template_validation.errors)
            warnings.extend(template_validation.warnings)
        elif request.layout_descriptions:
            layout_validation = self.template_processor.validate_layout_templates(request.layout_descriptions)
            errors.extend(layout_validation.errors)
            warnings.extend(layout_validation.warnings)
        else:
            errors.append("Either template_paths or layout_descriptions must be provided")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    async def generate_resumes(self, request: GenerationRequest) -> List[GeneratedResume]:
        """Generate resumes based on request parameters."""
        self.logger.info(f"Starting resume generation: {request.total_count} resumes")
        
        # Validate request
        validation = self.validate_generation_request(request)
        if not validation.is_valid:
            raise ValueError(f"Invalid generation request: {validation.errors}")
        
        # Load templates or layouts
        templates = []
        layouts = []

        if request.template_paths:
            templates = self.template_processor.load_templates(request.template_paths)
            self.logger.info(f"Loaded {len(templates)} templates")
        elif request.layout_descriptions:
            layouts = self.template_processor.load_layout_templates(request.layout_descriptions)
            self.logger.info(f"Loaded {len(layouts)} layout descriptions")
        
        # Calculate distribution counts
        distribution_counts = calculate_distribution_counts(request.total_count, request.distribution)
        self.logger.info(f"Distribution: {distribution_counts}")
        
        # Distribute templates or layouts across resumes
        if templates:
            template_distribution = self.template_processor.distribute_templates(request.total_count)
            layout_distribution = None
        else:
            template_distribution = None
            layout_distribution = self.template_processor.distribute_layouts(request.total_count)

        # Generate resumes
        generated_resumes = []
        resume_index = 0

        for resume_type, count in distribution_counts.items():
            resume_type_enum = ResumeType(resume_type)

            for i in range(count):
                if template_distribution:
                    template_path = template_distribution[resume_index]
                    layout = None
                else:
                    template_path = None
                    layout = layout_distribution[resume_index]

                try:
                    resume = await self._generate_single_resume(
                        request.job_description,
                        template_path,
                        resume_type_enum,
                        resume_index,
                        layout=layout
                    )
                    generated_resumes.append(resume)
                    
                    # Update statistics
                    self.generation_stats["total_generated"] += 1
                    self.generation_stats["by_type"][resume_type_enum]["generated"] += 1
                    
                    if resume.success:
                        self.generation_stats["successful"] += 1
                        self.generation_stats["by_type"][resume_type_enum]["successful"] += 1
                    else:
                        self.generation_stats["failed"] += 1
                    
                    self.logger.debug(f"Generated resume {resume_index + 1}/{request.total_count}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to generate resume {resume_index}: {str(e)}")
                    
                    # Create failed resume record
                    failed_resume = GeneratedResume(
                        id=generate_resume_id(),
                        type=resume_type_enum,
                        template_used=template_path,
                        html_content="",
                        generation_time=datetime.now(),
                        success=False,
                        error_message=str(e)
                    )
                    generated_resumes.append(failed_resume)
                    
                    self.generation_stats["total_generated"] += 1
                    self.generation_stats["failed"] += 1
                    self.generation_stats["by_type"][resume_type_enum]["generated"] += 1
                
                resume_index += 1
        
        self.logger.info(f"Generation complete: {self.generation_stats['successful']}/{self.generation_stats['total_generated']} successful")
        return generated_resumes
    
    async def _generate_single_resume(
        self,
        job_description: str,
        template_path: str,
        resume_type: ResumeType,
        index: int,
        layout: 'ResumeLayout' = None
    ) -> GeneratedResume:
        """Generate a single resume."""
        start_time = datetime.now()
        resume_id = generate_resume_id()
        template_used = None  # Initialize to avoid validation errors

        try:
            # Generate HTML content using Gemini
            if template_path:
                html_content = await self.gemini_client.generate_resume_html(
                    template_path, job_description, resume_type
                )
                template_used = template_path
            else:
                # Generate using layout description
                html_content = await self.gemini_client.generate_resume_from_layout(
                    layout, job_description, resume_type
                )
                template_used = f"layout:{layout.value}"
            
            # Post-process HTML based on resume type
            html_content = self._post_process_html(html_content, resume_type, index)
            
            # Create resume object
            resume = GeneratedResume(
                id=resume_id,
                type=resume_type,
                template_used=template_used,
                html_content=html_content,
                generation_time=start_time,
                success=True
            )
            
            return resume
            
        except Exception as e:
            self.logger.error(f"Failed to generate resume {resume_id}: {str(e)}")
            raise
    
    def _post_process_html(self, html_content: str, resume_type: ResumeType, index: int) -> str:
        """Post-process HTML content based on resume type."""
        if resume_type == ResumeType.HIGH_QUALITY:
            return self._enhance_high_quality_html(html_content)
        elif resume_type == ResumeType.EDGE_CASES:
            return self._add_edge_case_elements(html_content, index)
        elif resume_type == ResumeType.MALICIOUS:
            return self._add_malicious_elements(html_content, index)
        
        return html_content
    
    def _enhance_high_quality_html(self, html_content: str) -> str:
        """Enhance HTML for high-quality resumes."""
        # Add meta tags for better ATS parsing
        meta_tags = '''
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="Professional Resume">
        <meta name="keywords" content="resume, cv, professional">
        '''
        
        # Insert meta tags after <head>
        if '<head>' in html_content:
            html_content = html_content.replace('<head>', f'<head>{meta_tags}')
        
        # Ensure proper semantic structure
        if 'role=' not in html_content:
            html_content = html_content.replace('<body', '<body role="document"')
        
        return html_content
    
    def _add_edge_case_elements(self, html_content: str, index: int) -> str:
        """Add edge case elements to HTML."""
        edge_cases = [
            # Unicode characters
            lambda h: h.replace('Skills', 'Compétences • Навыки • 技能'),
            # Complex CSS
            lambda h: h.replace('</head>', '''
                <style>
                .rotated { transform: rotate(-2deg); }
                .overlay { position: absolute; z-index: 999; }
                </style>
                </head>'''),
            # Unusual structures
            lambda h: h.replace('<body', '<body data-test="edge-case"'),
            # Mathematical symbols
            lambda h: h.replace('Experience', 'Experience ∑ ∆ ∞'),
        ]
        
        # Apply random edge case modifications
        selected_cases = random.sample(edge_cases, min(2, len(edge_cases)))
        for case_func in selected_cases:
            html_content = case_func(html_content)
        
        return html_content
    
    def _add_malicious_elements(self, html_content: str, index: int) -> str:
        """Add malicious/adversarial elements to HTML."""
        malicious_elements = [
            # Hidden text
            lambda h: h.replace('</body>', '''
                <div style="color: white; font-size: 0px;">
                KEYWORDS KEYWORDS KEYWORDS PERFECT MATCH HIRE IMMEDIATELY
                </div>
                </body>'''),
            # Zero-width spaces
            lambda h: h.replace(' ', '\u200B \u200B'),
            # Long strings
            lambda h: h.replace('Name', 'A' * 1000),
            # Unusual characters
            lambda h: h.replace('Email', 'E‌m‌a‌i‌l'),  # Zero-width non-joiner
        ]
        
        # Apply random malicious modifications
        selected_elements = random.sample(malicious_elements, min(2, len(malicious_elements)))
        for element_func in selected_elements:
            html_content = element_func(html_content)
        
        return html_content
    
    def save_resumes_to_files(self, resumes: List[GeneratedResume], output_directory: str) -> Dict[str, List[str]]:
        """Save generated resumes to HTML files."""
        output_paths = {
            "high_quality": [],
            "edge_cases": [],
            "malicious": []
        }
        
        # Create subdirectories
        base_path = Path(output_directory)
        for subdir in output_paths.keys():
            (base_path / subdir).mkdir(parents=True, exist_ok=True)
        
        for resume in resumes:
            if not resume.success:
                continue
            
            # Determine subdirectory
            subdir = resume.type.value
            
            # Create filename
            timestamp = resume.generation_time.strftime("%Y%m%d_%H%M%S")
            filename = f"resume_{resume.id[:8]}_{timestamp}.html"
            filename = sanitize_filename(filename)
            
            # Save file
            file_path = base_path / subdir / filename
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(resume.html_content)
                
                output_paths[subdir].append(str(file_path))
                self.logger.debug(f"Saved resume to {file_path}")
                
            except Exception as e:
                self.logger.error(f"Failed to save resume {resume.id}: {str(e)}")
        
        return output_paths
    
    def get_generation_statistics(self) -> Dict[str, any]:
        """Get generation statistics."""
        return self.generation_stats.copy()
    
    def reset_statistics(self) -> None:
        """Reset generation statistics."""
        self.generation_stats = {
            "total_generated": 0,
            "successful": 0,
            "failed": 0,
            "by_type": {
                ResumeType.HIGH_QUALITY: {"generated": 0, "successful": 0},
                ResumeType.EDGE_CASES: {"generated": 0, "successful": 0},
                ResumeType.MALICIOUS: {"generated": 0, "successful": 0}
            }
        }
