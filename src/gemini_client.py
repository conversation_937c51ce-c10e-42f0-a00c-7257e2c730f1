"""Google Gemini Vision API client for template-based resume generation."""

import asyncio
import base64
import time
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
import requests
from PIL import Image

from .config import config
from .models import ResumeType, Template, ResumeLayout
from .utils import get_file_hash
from .layout_templates import LayoutTemplateGenerator


class GeminiClient:
    """Client for Google Gemini Vision API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini client."""
        self.api_key = api_key or config.gemini.api_key
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        # Configure the API
        genai.configure(api_key=self.api_key)
        
        # Initialize model
        self.model = genai.GenerativeModel(config.gemini.model)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 60 / config.gemini.rate_limit  # seconds between requests
        
        self.logger = logging.getLogger(__name__)

        # Initialize layout generator
        self.layout_generator = LayoutTemplateGenerator()
    
    def _wait_for_rate_limit(self):
        """Ensure rate limiting compliance."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _load_image(self, image_path: str) -> Image.Image:
        """Load and validate image file."""
        try:
            image = Image.open(image_path)
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            return image
        except Exception as e:
            raise ValueError(f"Failed to load image {image_path}: {str(e)}")
    
    def _create_prompt(self, job_description: str, resume_type: ResumeType) -> str:
        """Create generation prompt based on resume type."""
        base_prompt = f"""
        Generate a complete HTML resume document that matches the visual layout and design of the provided template image.

        Job Description: {job_description}

        Requirements:
        - Create a complete HTML document with embedded CSS
        - Match the visual layout, typography, and design elements from the template
        - Tailor the content to be relevant for the provided job description
        - Include realistic personal information, experience, education, and skills
        - Use proper semantic HTML structure
        - Ensure the design is responsive and print-friendly

        CRITICAL OUTPUT REQUIREMENTS:
        - Output ONLY the raw HTML code starting with <!DOCTYPE html>
        - Do NOT include any markdown formatting like ```html or ```
        - Do NOT include any explanations, comments, or additional text
        - Do NOT add phrases like "Key improvements and explanations:" or similar
        - The response must start immediately with <!DOCTYPE html> and end with </html>
        - No text before or after the HTML code
        """
        
        if resume_type == ResumeType.HIGH_QUALITY:
            return base_prompt + """
            
            Additional Requirements for High-Quality Resume:
            - Use ATS-friendly formatting with clear section headers
            - Include standard sections: Contact, Summary, Experience, Education, Skills
            - Use professional fonts (Arial, Calibri, Times New Roman)
            - Maintain 10-12pt font size for body text
            - Include 5-15% keyword match rate with the job description
            - Use clean, semantic HTML with proper accessibility markup
            - Ensure consistent formatting and professional appearance
            """
        
        elif resume_type == ResumeType.EDGE_CASES:
            return base_prompt + """
            
            Additional Requirements for Edge Case Resume:
            - Use non-standard HTML structures (nested tables, CSS Grid/Flexbox)
            - Include multilingual content (English + one other language)
            - Add Unicode characters, symbols, or mathematical notation
            - Use unconventional section names like "My Journey", "Core Competencies"
            - Include embedded SVG icons or CSS-generated content
            - Use extreme typography (8pt or 18pt fonts, unusual line-heights)
            - Implement complex CSS layouts with absolute positioning or rotated text
            """
        
        elif resume_type == ResumeType.MALICIOUS:
            return base_prompt + """
            
            Additional Requirements for Adversarial Resume:
            - Include hidden text techniques (white text on white background)
            - Add keyword stuffing with zero font-size text
            - Include unusual HTML structures that might confuse parsers
            - Use steganographic techniques like zero-width spaces
            - Add extremely long strings in certain fields (1000+ characters)
            - Include special characters and escape sequences
            - Use malformed HTML tags or unclosed elements (but keep it parseable)
            - Add invisible Unicode characters between words
            """
        
        return base_prompt

    def _get_resume_type_instructions(self, resume_type: ResumeType) -> str:
        """Get resume type specific instructions."""
        if resume_type == ResumeType.HIGH_QUALITY:
            return """
            Additional Requirements for High-Quality Resume:
            - Use ATS-friendly formatting with clear section headers
            - Include standard sections: Contact, Summary, Experience, Education, Skills
            - Use professional fonts (Arial, Calibri, Times New Roman)
            - Maintain 10-12pt font size for body text
            - Include 5-15% keyword match rate with the job description
            - Use clean, semantic HTML with proper accessibility markup
            - Ensure consistent formatting and professional appearance
            """

        elif resume_type == ResumeType.EDGE_CASES:
            return """
            Additional Requirements for Edge Case Resume:
            - Use non-standard HTML structures (nested tables, CSS Grid/Flexbox)
            - Include multilingual content (English + one other language)
            - Add Unicode characters, symbols, or mathematical notation
            - Use unconventional section names like "My Journey", "Core Competencies"
            - Include embedded SVG icons or CSS-generated content
            - Use extreme typography (8pt or 18pt fonts, unusual line-heights)
            - Implement complex CSS layouts with absolute positioning or rotated text
            """

        elif resume_type == ResumeType.MALICIOUS:
            return """
            Additional Requirements for Adversarial Resume:
            - Include hidden text techniques (white text on white background)
            - Add keyword stuffing with zero font-size text
            - Include unusual HTML structures that might confuse parsers
            - Use steganographic techniques like zero-width spaces
            - Add extremely long strings in certain fields (1000+ characters)
            - Include special characters and escape sequences
            - Use malformed HTML tags or unclosed elements (but keep it parseable)
            - Add invisible Unicode characters between words
            """

        return ""

    async def generate_resume_html(
        self, 
        template_path: str, 
        job_description: str, 
        resume_type: ResumeType
    ) -> str:
        """Generate HTML resume based on template and job description."""
        
        # Rate limiting
        self._wait_for_rate_limit()
        
        # Load template image
        template_image = self._load_image(template_path)
        
        # Create prompt
        prompt = self._create_prompt(job_description, resume_type)
        
        # Retry logic
        for attempt in range(config.gemini.max_retries):
            try:
                self.logger.debug(f"Generating resume (attempt {attempt + 1}/{config.gemini.max_retries})")
                
                # Generate content
                response = self.model.generate_content(
                    [prompt, template_image],
                    safety_settings={
                        HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                        HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                        HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                        HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
                    }
                )
                
                if response.text:
                    html_content = response.text.strip()
                    # Clean any markdown formatting or unwanted text
                    html_content = self._clean_html_response(html_content)

                    # Basic validation
                    if self._validate_html_response(html_content):
                        self.logger.debug("Successfully generated HTML resume")
                        return html_content
                    else:
                        raise ValueError("Generated HTML failed validation")
                else:
                    raise ValueError("Empty response from Gemini API")
                    
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed: {str(e)}")
                
                if attempt < config.gemini.max_retries - 1:
                    delay = config.gemini.retry_delays[min(attempt, len(config.gemini.retry_delays) - 1)]
                    self.logger.debug(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    raise Exception(f"Failed to generate resume after {config.gemini.max_retries} attempts: {str(e)}")
    
    def _validate_html_response(self, html_content: str) -> bool:
        """Basic validation of HTML response."""
        # Check for basic HTML structure
        html_lower = html_content.lower()
        
        required_tags = ['<html', '<head', '<body']
        for tag in required_tags:
            if tag not in html_lower:
                self.logger.warning(f"Missing required HTML tag: {tag}")
                return False
        
        # Check minimum length
        if len(html_content) < 500:
            self.logger.warning("HTML content too short")
            return False
        
        return True
    
    def get_template_info(self, template_path: str) -> Dict[str, Any]:
        """Get information about a template file."""
        path = Path(template_path)
        
        if not path.exists():
            raise FileNotFoundError(f"Template file not found: {template_path}")
        
        # Load image to get dimensions
        try:
            with Image.open(template_path) as img:
                width, height = img.size
                mode = img.mode
        except Exception as e:
            raise ValueError(f"Failed to read image: {str(e)}")
        
        # Get file info
        file_size = path.stat().st_size
        file_hash = get_file_hash(template_path)
        
        return {
            "path": str(path),
            "filename": path.name,
            "file_size_bytes": file_size,
            "file_size_mb": file_size / (1024 * 1024),
            "dimensions": (width, height),
            "mode": mode,
            "hash": file_hash
        }
    
    async def test_connection(self) -> bool:
        """Test connection to Gemini API."""
        try:
            # Simple test with text-only model
            test_model = genai.GenerativeModel('gemini-pro')
            response = test_model.generate_content("Hello, this is a test.")
            return bool(response.text)
        except Exception as e:
            self.logger.error(f"Gemini API connection test failed: {str(e)}")
            return False

    async def _generate_content_with_retry(self, prompt: str) -> str:
        """Generate content with retry logic for text-only prompts."""
        # Rate limiting
        self._wait_for_rate_limit()

        # Retry logic
        for attempt in range(config.gemini.max_retries):
            try:
                self.logger.debug(f"Generating content (attempt {attempt + 1}/{config.gemini.max_retries})")

                # Generate content
                response = self.model.generate_content(
                    prompt,
                    safety_settings={
                        HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                        HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                        HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                        HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
                    }
                )

                if response.text:
                    html_content = response.text.strip()
                    # Clean any markdown formatting or unwanted text
                    html_content = self._clean_html_response(html_content)
                    self.logger.debug(f"Generated {len(html_content)} characters")
                    return html_content
                else:
                    raise ValueError("Empty response from Gemini API")

            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed: {str(e)}")
                if attempt < config.gemini.max_retries - 1:
                    delay = config.gemini.retry_delays[min(attempt, len(config.gemini.retry_delays) - 1)]
                    self.logger.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    raise ValueError(f"Failed to generate content after {config.gemini.max_retries} attempts: {str(e)}")

    async def generate_resume_from_layout(
        self,
        layout: ResumeLayout,
        job_description: str,
        resume_type: ResumeType
    ) -> str:
        """Generate resume HTML from layout description."""
        self.logger.info(f"Generating {resume_type.value} resume using {layout.value} layout")

        # Get layout-specific prompt
        layout_prompt = self.layout_generator.generate_layout_prompt(layout, job_description)

        # Add resume type specific instructions
        type_instructions = self._get_resume_type_instructions(resume_type)

        # Combine prompts
        full_prompt = f"{layout_prompt}\n\n{type_instructions}"

        # Generate content
        html_content = await self._generate_content_with_retry(full_prompt)

        # Clean any markdown formatting or unwanted text
        html_content = self._clean_html_response(html_content)

        # Get layout CSS and inject it
        css_content = self.layout_generator.get_css_template(layout)
        html_content = self._inject_css(html_content, css_content)

        return html_content

    def _inject_css(self, html_content: str, css_content: str) -> str:
        """Inject CSS into HTML content."""
        # Find the head section or create one
        if '<head>' in html_content:
            # Insert CSS before closing head tag
            css_tag = f"<style>\n{css_content}\n</style>\n</head>"
            html_content = html_content.replace('</head>', css_tag)
        else:
            # Add head section with CSS
            css_section = f"<head>\n<style>\n{css_content}\n</style>\n</head>"
            if '<html>' in html_content:
                html_content = html_content.replace('<html>', f'<html>\n{css_section}')
            else:
                html_content = f"<html>\n{css_section}\n<body>\n{html_content}\n</body>\n</html>"

        return html_content

    def _clean_html_response(self, html_content: str) -> str:
        """Clean HTML response from any markdown formatting or unwanted text."""
        # Remove markdown code blocks
        if html_content.startswith('```html'):
            html_content = html_content[7:]  # Remove ```html
        if html_content.startswith('```'):
            html_content = html_content[3:]  # Remove ```
        if html_content.endswith('```'):
            html_content = html_content[:-3]  # Remove trailing ```

        # Remove common unwanted phrases that might appear before HTML
        unwanted_phrases = [
            "Here's the HTML code:",
            "Here is the HTML:",
            "Key improvements and explanations:",
            "The HTML code is:",
            "HTML code:",
            "Here's a complete HTML resume:",
            "Here is a complete HTML resume:",
            "Below is the HTML:",
            "The complete HTML document:",
        ]

        for phrase in unwanted_phrases:
            if html_content.strip().startswith(phrase):
                html_content = html_content.strip()[len(phrase):].strip()

        # Remove any text before <!DOCTYPE html> if it exists
        doctype_index = html_content.find('<!DOCTYPE html>')
        if doctype_index > 0:
            html_content = html_content[doctype_index:]
        elif html_content.find('<html') > 0:
            # If no DOCTYPE, look for <html tag
            html_index = html_content.find('<html')
            if html_index > 0:
                html_content = html_content[html_index:]

        # Remove any text after </html> if it exists
        html_end_index = html_content.rfind('</html>')
        if html_end_index > 0:
            html_content = html_content[:html_end_index + 7]  # Include </html>

        return html_content.strip()
