"""Utility functions for ATS CV Generator."""

import os
import uuid
import logging
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import json


def setup_logging(config) -> logging.Logger:
    """Setup logging configuration."""
    logger = logging.getLogger("cv_generator")
    logger.setLevel(getattr(logging, config.logging.level.upper()))
    
    # Create formatter
    formatter = logging.Formatter(config.logging.format)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    file_handler = logging.FileHandler(config.logging.file)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def generate_job_id() -> str:
    """Generate unique job ID."""
    return str(uuid.uuid4())


def generate_resume_id() -> str:
    """Generate unique resume ID."""
    return str(uuid.uuid4())


def calculate_distribution_counts(total_count: int, distribution: Dict[str, int]) -> Dict[str, int]:
    """Calculate exact counts for each resume type based on distribution percentages."""
    counts = {}
    remaining = total_count
    
    # Calculate counts for each type except the last one
    types = list(distribution.keys())
    for i, resume_type in enumerate(types[:-1]):
        count = int(total_count * distribution[resume_type] / 100)
        counts[resume_type] = count
        remaining -= count
    
    # Assign remaining count to the last type (handles rounding)
    counts[types[-1]] = remaining
    
    return counts


def validate_template_file(file_path: str, max_size_mb: int = 10) -> Tuple[bool, str]:
    """Validate template file."""
    path = Path(file_path)
    
    if not path.exists():
        return False, f"File does not exist: {file_path}"
    
    if path.suffix.lower() not in ['.jpg', '.jpeg']:
        return False, f"File must be JPEG format: {file_path}"
    
    file_size_mb = path.stat().st_size / (1024 * 1024)
    if file_size_mb > max_size_mb:
        return False, f"File size ({file_size_mb:.1f}MB) exceeds limit ({max_size_mb}MB)"
    
    return True, "Valid"


def create_output_directory(base_path: str, job_id: str) -> str:
    """Create output directory structure."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(base_path) / f"generated_resumes_{timestamp}_{job_id[:8]}"
    
    # Create subdirectories
    subdirs = ["high_quality", "edge_cases", "malicious", "templates_used"]
    for subdir in subdirs:
        (output_dir / subdir).mkdir(parents=True, exist_ok=True)
    
    return str(output_dir)


def get_file_hash(file_path: str) -> str:
    """Calculate MD5 hash of file."""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


def save_json_report(data: dict, file_path: str) -> None:
    """Save data as JSON report."""
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, default=str, ensure_ascii=False)


def load_json_report(file_path: str) -> dict:
    """Load JSON report."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"


def format_duration(seconds: float) -> str:
    """Format duration in human readable format."""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file system usage."""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Limit length
    if len(filename) > 255:
        filename = filename[:255]
    
    return filename


def estimate_processing_time(total_count: int, concurrent_threads: int = 4) -> float:
    """Estimate processing time in seconds."""
    # Rough estimate: 2-5 seconds per resume depending on complexity
    avg_time_per_resume = 3.5
    return (total_count * avg_time_per_resume) / concurrent_threads


def validate_distribution_percentages(distribution: Dict[str, int]) -> Tuple[bool, str]:
    """Validate distribution percentages."""
    if sum(distribution.values()) != 100:
        return False, f"Distribution percentages sum to {sum(distribution.values())}, must equal 100"
    
    required_keys = {"high_quality", "edge_cases", "malicious"}
    if not required_keys.issubset(distribution.keys()):
        missing = required_keys - distribution.keys()
        return False, f"Missing required distribution keys: {missing}"
    
    for key, value in distribution.items():
        if not isinstance(value, int) or value < 0:
            return False, f"Distribution value for '{key}' must be a non-negative integer"
    
    return True, "Valid"
