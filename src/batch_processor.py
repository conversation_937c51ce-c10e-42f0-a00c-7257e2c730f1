"""Batch processing system with progress tracking and concurrency control."""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import json
from pathlib import Path

from .config import config
from .models import GenerationRequest, GenerationJob, GeneratedResume, GenerationReport
from .resume_generator import ResumeGenerator
from .pdf_converter import PDFConverter
from .template_processor import TemplateProcessor
from .utils import (
    generate_job_id, create_output_directory, save_json_report,
    format_duration, estimate_processing_time
)


class BatchProcessor:
    """Handles batch processing of resume generation with progress tracking."""
    
    def __init__(self):
        """Initialize batch processor."""
        self.logger = logging.getLogger(__name__)
        self.active_jobs: Dict[str, GenerationJob] = {}
        self.resume_generator = ResumeGenerator()
        self.template_processor = TemplateProcessor()
        
        # Progress tracking
        self.progress_callbacks: List[Callable] = []
        
        # Processing statistics
        self.processing_stats = {
            "total_jobs": 0,
            "completed_jobs": 0,
            "failed_jobs": 0,
            "total_resumes_generated": 0,
            "total_processing_time": 0.0
        }
    
    def add_progress_callback(self, callback: Callable[[str, float, Dict], None]) -> None:
        """Add progress callback function."""
        self.progress_callbacks.append(callback)
    
    def _notify_progress(self, job_id: str, progress: float, details: Dict[str, Any]) -> None:
        """Notify progress callbacks."""
        for callback in self.progress_callbacks:
            try:
                callback(job_id, progress, details)
            except Exception as e:
                self.logger.error(f"Progress callback error: {str(e)}")
    
    async def process_generation_request(
        self, 
        request: GenerationRequest,
        enable_pdf_conversion: bool = True
    ) -> GenerationJob:
        """Process a complete generation request."""
        job_id = generate_job_id()
        
        # Create generation job
        job = GenerationJob(
            job_id=job_id,
            request=request,
            status="pending",
            total_resumes=request.total_count
        )
        
        self.active_jobs[job_id] = job
        self.processing_stats["total_jobs"] += 1
        
        try:
            # Start processing
            job.status = "running"
            job.started_at = datetime.now()
            
            self.logger.info(f"Starting batch processing job {job_id}")
            self._notify_progress(job_id, 0.0, {"status": "starting", "phase": "initialization"})
            
            # Create output directory
            output_directory = create_output_directory(
                config.output.base_directory, 
                job_id
            )
            job.output_directory = output_directory
            
            # Phase 1: Generate resumes (70% of progress)
            self._notify_progress(job_id, 5.0, {"status": "running", "phase": "generating_resumes"})
            
            resumes = await self._generate_resumes_with_progress(request, job_id)
            job.completed_resumes = len([r for r in resumes if r.success])
            job.failed_resumes = len([r for r in resumes if not r.success])
            
            self._notify_progress(job_id, 70.0, {
                "status": "running", 
                "phase": "resumes_generated",
                "completed": job.completed_resumes,
                "failed": job.failed_resumes
            })
            
            # Phase 2: Save HTML files (10% of progress)
            self._notify_progress(job_id, 75.0, {"status": "running", "phase": "saving_html"})
            
            html_paths = self.resume_generator.save_resumes_to_files(resumes, output_directory)
            
            self._notify_progress(job_id, 80.0, {"status": "running", "phase": "html_saved"})
            
            # Phase 3: Convert to PDF (15% of progress)
            pdf_paths = {}
            if enable_pdf_conversion:
                self._notify_progress(job_id, 85.0, {"status": "running", "phase": "converting_pdf"})
                
                async with PDFConverter() as pdf_converter:
                    pdf_paths = await pdf_converter.convert_resumes_batch(resumes, output_directory)
                
                self._notify_progress(job_id, 95.0, {"status": "running", "phase": "pdf_converted"})
            
            # Phase 4: Copy templates and generate report (5% of progress)
            self._notify_progress(job_id, 97.0, {"status": "running", "phase": "finalizing"})
            
            # Copy templates to output
            template_paths = self.template_processor.copy_templates_to_output(output_directory)
            
            # Save template metadata
            self.template_processor.save_template_metadata(output_directory)
            
            # Generate final report
            report = self._generate_report(job, resumes, html_paths, pdf_paths, template_paths)
            report_path = Path(output_directory) / config.output.report_filename
            save_json_report(report.dict(), str(report_path))
            
            # Complete job
            job.status = "completed"
            job.completed_at = datetime.now()
            job.progress = 100.0
            
            self.processing_stats["completed_jobs"] += 1
            self.processing_stats["total_resumes_generated"] += job.completed_resumes
            
            if job.started_at:
                processing_time = (job.completed_at - job.started_at).total_seconds()
                self.processing_stats["total_processing_time"] += processing_time
            
            self._notify_progress(job_id, 100.0, {
                "status": "completed",
                "output_directory": output_directory,
                "completed_resumes": job.completed_resumes,
                "failed_resumes": job.failed_resumes
            })
            
            self.logger.info(f"Batch processing job {job_id} completed successfully")
            
        except Exception as e:
            job.status = "failed"
            job.error_message = str(e)
            job.completed_at = datetime.now()
            
            self.processing_stats["failed_jobs"] += 1
            
            self.logger.error(f"Batch processing job {job_id} failed: {str(e)}")
            self._notify_progress(job_id, 0.0, {
                "status": "failed",
                "error": str(e)
            })
            
            raise
        
        return job
    
    async def _generate_resumes_with_progress(
        self, 
        request: GenerationRequest, 
        job_id: str
    ) -> List[GeneratedResume]:
        """Generate resumes with progress tracking."""
        
        # Use semaphore to limit concurrent generations
        semaphore = asyncio.Semaphore(config.processing.max_concurrent_threads)
        
        async def generate_with_semaphore(index: int, total: int) -> GeneratedResume:
            async with semaphore:
                # This is a simplified version - in reality, we'd need to integrate
                # with the resume generator's internal progress tracking
                progress = 10.0 + (index / total) * 60.0  # 10% to 70%
                self._notify_progress(job_id, progress, {
                    "status": "running",
                    "phase": "generating_resumes",
                    "current": index + 1,
                    "total": total
                })
                
                # For now, delegate to the resume generator
                # In a full implementation, we'd break this down further
                return await self.resume_generator.generate_resumes(request)
        
        # Generate resumes
        resumes = await self.resume_generator.generate_resumes(request)
        
        return resumes
    
    def _generate_report(
        self,
        job: GenerationJob,
        resumes: List[GeneratedResume],
        html_paths: Dict[str, List[str]],
        pdf_paths: Dict[str, List[str]],
        template_paths: Dict[str, str]
    ) -> GenerationReport:
        """Generate comprehensive generation report."""
        
        # Calculate statistics
        total_resumes = len(resumes)
        successful_resumes = len([r for r in resumes if r.success])
        failed_resumes = total_resumes - successful_resumes
        
        # Success rates by type
        success_rates = {}
        for resume_type in ["high_quality", "edge_cases", "malicious"]:
            type_resumes = [r for r in resumes if r.type.value == resume_type]
            type_successful = [r for r in type_resumes if r.success]
            if type_resumes:
                success_rates[resume_type] = len(type_successful) / len(type_resumes)
            else:
                success_rates[resume_type] = 0.0
        
        # Processing time
        processing_time = 0.0
        if job.started_at and job.completed_at:
            processing_time = (job.completed_at - job.started_at).total_seconds()
        
        # Template usage statistics
        template_usage = self.template_processor.get_template_statistics()
        
        # Generation statistics
        generation_stats = {
            "total_requested": job.request.total_count,
            "total_generated": total_resumes,
            "successful": successful_resumes,
            "failed": failed_resumes,
            "success_rate": successful_resumes / total_resumes if total_resumes > 0 else 0.0,
            "distribution_actual": {
                "high_quality": len([r for r in resumes if r.type.value == "high_quality"]),
                "edge_cases": len([r for r in resumes if r.type.value == "edge_cases"]),
                "malicious": len([r for r in resumes if r.type.value == "malicious"])
            },
            "distribution_requested": job.request.distribution,
            "processing_time_seconds": processing_time,
            "processing_time_formatted": format_duration(processing_time),
            "resumes_per_minute": (successful_resumes / (processing_time / 60)) if processing_time > 0 else 0.0
        }
        
        # Output paths
        output_paths = {
            "html_files": html_paths,
            "pdf_files": pdf_paths,
            "templates": template_paths,
            "output_directory": job.output_directory or ""
        }
        
        # Collect errors
        errors = [r.error_message for r in resumes if r.error_message]
        if job.error_message:
            errors.append(job.error_message)
        
        return GenerationReport(
            job_id=job.job_id,
            request_parameters={
                "job_description_length": len(job.request.job_description),
                "total_count": job.request.total_count,
                "distribution": job.request.distribution,
                "template_count": len(job.request.template_paths) if job.request.template_paths else 0,
                "layout_count": len(job.request.layout_descriptions) if job.request.layout_descriptions else 0
            },
            generation_statistics=generation_stats,
            template_usage=template_usage.get("usage_stats", {}),
            success_rates=success_rates,
            processing_time=processing_time,
            output_paths=output_paths,
            errors=errors
        )
    
    def get_job_status(self, job_id: str) -> Optional[GenerationJob]:
        """Get status of a specific job."""
        return self.active_jobs.get(job_id)
    
    def list_active_jobs(self) -> List[GenerationJob]:
        """List all active jobs."""
        return list(self.active_jobs.values())
    
    def cleanup_completed_jobs(self, max_age_hours: int = 24) -> int:
        """Clean up completed jobs older than specified age."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        jobs_to_remove = []
        for job_id, job in self.active_jobs.items():
            if (job.status in ["completed", "failed"] and 
                job.completed_at and 
                job.completed_at < cutoff_time):
                jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            del self.active_jobs[job_id]
        
        self.logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")
        return len(jobs_to_remove)
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get overall processing statistics."""
        stats = self.processing_stats.copy()
        
        if stats["completed_jobs"] > 0:
            stats["average_processing_time"] = stats["total_processing_time"] / stats["completed_jobs"]
            stats["average_resumes_per_job"] = stats["total_resumes_generated"] / stats["completed_jobs"]
        
        stats["active_jobs"] = len([j for j in self.active_jobs.values() if j.status == "running"])
        
        return stats
    
    def estimate_completion_time(self, request: GenerationRequest) -> float:
        """Estimate completion time for a generation request."""
        return estimate_processing_time(
            request.total_count,
            config.processing.max_concurrent_threads
        )
